#include "stdafx.h"
#include "EncodeConvertor.h"
#include <windows.h>
#include <string>
#include <memory>

bool EncodeConvertor::UTF8ToMBCS(rse::string& tstr, const char* utf8)
{
	if (utf8 == nullptr)
	{
		tstr.clear();
		return true;
	}

	auto utf8_len = strlen(utf8);
	if (utf8_len == 0)
	{
		tstr.clear();
		return true;
	}
	// convert an UTF8 string to widechar   
	auto wlen = MultiByteToWideChar(CP_UTF8, 0, utf8, utf8_len, NULL, 0);

	std::unique_ptr<WCHAR[], rse::RseArrayDeleter<WCHAR>> wbuf(rs_new_array(WCHAR, wlen));

	auto ret = MultiByteToWideChar(CP_UTF8, 0, utf8, utf8_len, wbuf.get(), wlen);

	if (ret != wlen)
	{
		return false;
	}

	// convert an widechar string to Multibyte   
	auto mbcs_len = WideCharToMultiByte(CP_ACP, 0, wbuf.get(), wlen, NULL, 0, NULL, NULL);
	if (mbcs_len <= 0)
	{
		return false;
	}

	std::unique_ptr<char[], rse::RseArrayDeleter<char>> mbcs_buf(rs_new_array(char, mbcs_len + 1));

	ret = WideCharToMultiByte(CP_ACP, 0, wbuf.get(), wlen, mbcs_buf.get(), mbcs_len, NULL, NULL);

	if (ret == mbcs_len)
	{
		mbcs_buf[mbcs_len] = 0;
		tstr = mbcs_buf.get();

		return true;
	}

	return false;
}



bool EncodeConvertor::MBCSToUTF8(rse::string& tstr, const char* mbcs)
{
	if (mbcs == nullptr)
	{
		tstr.clear();
		return true;
	}

	auto mbcs_len1 = strlen(mbcs);
	if (mbcs_len1 == 0)
	{
		tstr.clear();
		return true;
	}
	// convert an Multibyte string to widechar   
	auto wlen = MultiByteToWideChar(CP_ACP, 0, mbcs, mbcs_len1, NULL, 0);

	std::unique_ptr<WCHAR[], rse::RseArrayDeleter<WCHAR>> wbuf(rs_new_array(WCHAR, wlen));

	auto ret = MultiByteToWideChar(CP_ACP, 0, mbcs, mbcs_len1, wbuf.get(), wlen);

	if (ret != wlen)
	{
		return false;
	}

	// convert an widechar string to UTF8   
	auto utf8_len = WideCharToMultiByte(CP_UTF8, 0, wbuf.get(), wlen, NULL, 0, NULL, NULL);
	if (utf8_len <= 0)
	{
		return false;
	}

	std::unique_ptr<char[], rse::RseArrayDeleter<char>> utf8_buf(rs_new_array(char, utf8_len + 1));

	ret = WideCharToMultiByte(CP_UTF8, 0, wbuf.get(), wlen, utf8_buf.get(), utf8_len, NULL, NULL);

	if (ret == utf8_len)
	{
		utf8_buf[utf8_len] = 0;
		tstr = utf8_buf.get();
		return true;
	}

	return false;
}

bool EncodeConvertor::UTF16ToUTF8(rse::string& tstr, const wchar_t* wbcs)
{
	auto wlen = wcslen(wbcs);
	// convert an widechar string to UTF8   
	auto utf8_len = WideCharToMultiByte(CP_UTF8, 0, wbcs, wlen, NULL, 0, NULL, NULL);
	if (utf8_len <= 0)
	{
		return false;
	}

	std::unique_ptr<char[], rse::RseArrayDeleter<char>> utf8_buf(rs_new_array(char, utf8_len + 1));

	auto ret = WideCharToMultiByte(CP_UTF8, 0, wbcs, wlen, utf8_buf.get(), utf8_len, NULL, NULL);

	if (ret == utf8_len)
	{
		utf8_buf[utf8_len] = 0;
		tstr = utf8_buf.get();
		return true;
	}

	return false;
}
