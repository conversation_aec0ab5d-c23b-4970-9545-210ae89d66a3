﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TestDVNetWrapper
{
    /// <summary>
    /// 测试前需要编译对应版本的 引擎，装潢程序（需要拷贝到plugin目录中），RseDiscoverGraph, DecorationVRInterface
    /// 然后将解决方案目录中“yd”文件夹拷贝到dll所在目录
    /// </summary>
    public class Test
    {
        DVNetWrapper.DecorationVRInterface _vrObj;
        private bool _initVr = false;
        DVNetWrapper.ManagedElevatorSpecification CreateSpec(int configId)
        {
            var es = new DVNetWrapper.ManagedElevatorSpecification();
            if (configId == 1001 || configId == 1004)
            {
                es.Depth = 190;
                es.Width = 192;
                es.Height = 320;
                es.DoorHeight = 240;
                es.DoorWidth = 110;
            }
            else
            {
                es.Depth = 160;
                es.Width = 170;
                es.Height = 260;
                es.DoorHeight = 230;
                es.DoorWidth = 105;
            }

            return es;
        }

        DVNetWrapper.IDecorationVRCallBack _callBack = new Class1();


        public void Initialize(IntPtr wndHandle, int w, int h)
        {
            _vrObj = new DVNetWrapper.DecorationVRInterface();
            var dir = AppDomain.CurrentDomain.BaseDirectory + "yd\\";
            //_vrObj.SetDataBasePath(dir + "cpp_sd.db");
            _vrObj.SetResousePath(dir);
            var es = CreateSpec(1001);
            _vrObj.InitializeElevator(wndHandle, es, DVNetWrapper.ManagedInitType.IT_NEED_CREATE);
            Resize(w, h);
            _vrObj.SetPart((int)DVNetWrapper.ManagedPartTypeId.PartTypeCarConfig, 1001, true);
            _vrObj.SetDecorationVrCallBack(_callBack);
            _initVr = true;

        }

        public void Resize(int w, int h)
        {
            if (_vrObj != null)
            {
                _vrObj.ResizeRender();
                _vrObj.Resize(w, h);
            }
        }

        public void Render()
        {
            if (_initVr)
            {
                _vrObj.DoVrProcess();
            }
        }

        public void ChangeConfig(int configId)
        {
            var es = CreateSpec(configId);
            _vrObj.ChangeElevatorSpecification(es);
            _vrObj.SetPart((int)DVNetWrapper.ManagedPartTypeId.PartTypeCarConfig, configId, true);
        }
    }
}
