#pragma once
#include <string>

//#include <comutil.h>
//#include <memory>
//#pragma comment(lib, "comsupp.lib")

#ifdef _UNICODE 
typedef std::wstring tstring;
#elif defined(UNICODE)
typedef std::wstring tstring;
#else
typedef rse::string tstring;
#endif

using namespace System::Runtime::InteropServices;


class CNetTypeUtil
{
public:
	static tstring ToNativeString(System::String^ str_managed)
	{

#if defined(_UNICODE) || defined(UNICODE)
		IntPtr p = Marshal::StringToHGlobalUni(str_managed);
		tstring str = static_cast<wchar_t*>(p.ToPointer());
		Marshal::FreeHGlobal(p);
#else
		IntPtr p = Marshal::StringToHGlobalAnsi(str_managed);
		tstring str = static_cast<char*>(p.ToPointer());
		Marshal::FreeHGlobal(p);
#endif
		return str;
	}

	static rse::string ToNativeMBCSString(System::String^ str_managed)
	{
		IntPtr p = Marshal::StringToHGlobalAnsi(str_managed);
		rse::string str = static_cast<char*>(p.ToPointer());
		Marshal::FreeHGlobal(p);
		return str;
	}

	static System::String^ ToManagedString(const char* pstr_navtive)
	{
		return gcnew System::String(pstr_navtive);
	}
};