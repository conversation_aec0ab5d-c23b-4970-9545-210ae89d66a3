﻿========================================================================
    动态链接库：DVNetWrapper 项目概述
========================================================================

应用程序向导已为您创建了此 DVNetWrapper DLL。

本文件概要介绍组成 DVNetWrapper 应用程序的每个文件的内容。

DVNetWrapper.vcxproj
    这是使用应用程序向导生成的 VC++ 项目的主项目文件，其中包含生成该文件的 Visual C++ 的版本信息，以及有关使用应用程序向导选择的平台、配置和项目功能的信息。

DVNetWrapper.vcxproj.filters
    这是使用“应用程序向导”生成的 VC++ 项目筛选器文件。它包含有关项目文件与筛选器之间的关联信息。在 IDE 中，通过这种关联，在特定节点下以分组形式显示具有相似扩展名的文件。例如，“.cpp”文件与“源文件”筛选器关联。

DVNetWrapper.cpp
    这是主 DLL 源文件。

DVNetWrapper.h
    此文件包含类声明。

AssemblyInfo.cpp
	包含用于修改程序集元数据的自定义特性。

/////////////////////////////////////////////////////////////////////////////
其他注释:

应用程序向导使用“TODO:”来指示应添加或自定义的源代码部分。

/////////////////////////////////////////////////////////////////////////////
