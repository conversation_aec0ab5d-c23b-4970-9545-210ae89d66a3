#pragma once

#include <gcroot.h>
#include "i_decoration_vr_callback.h"
#include "NetTypeUtil.h"
#include "EncodeConvertor.h"

using namespace System::Runtime::InteropServices;

namespace DVNetWrapper
{
	public interface class IDecorationVRCallBack
	{
		void LoadCompleteCallBack(int wparam, int vali, System::String^ vals);
		System::String^ DownloadData(System::String^ download_url, System::String^ post_data);
		bool DownloadFile(System::String^ download_url, System::String^ file_abs_path);
		void OnProgress(int category, int progress, System::String^ info);
		bool GetReceiveProgress() = 0;
		void SetReceiveProgress(bool val) = 0;
	};

	class DVDecorationCallBack : public decoration_vr_interface::IDecorationVrCallBack
	{
	public:
		~DVDecorationCallBack()
		{
			managed_ptr_ = nullptr;
		}

		virtual void LoadCompleteCallBack(int wparam, int vali, const char* vals) override
		{
			if ((IDecorationVRCallBack^)managed_ptr_ != nullptr)
			{
				System::String^ sval = nullptr;
				if (vals != nullptr)
				{
					sval = CNetTypeUtil::ToManagedString(vals);
				}

				managed_ptr_->LoadCompleteCallBack(wparam, vali, sval);
			}
		}

		virtual char* DownloadData(const char* download_url, const char *post_data) override
		{
			System::String^ url_sval = CNetTypeUtil::ToManagedString(download_url);
			System::String^ post_sval = CNetTypeUtil::ToManagedString(post_data);
			System::String^ ret_val = managed_ptr_->DownloadData(url_sval, post_sval);
			if (ret_val != nullptr)
			{
				IntPtr p = Marshal::StringToHGlobalUni(ret_val);
				rse::string utf8_str;
			
				EncodeConvertor::UTF16ToUTF8(utf8_str, (wchar_t*)p.ToPointer());
				Marshal::FreeHGlobal(p);

				auto len = utf8_str.length();
				auto ret = rs_new_array(char, len + 1);
				auto x = utf8_str.data();
				memcpy(ret, x, len);
				//strcpy_s(ret, len, x);
				//strcpy_s(ret, utf8_str.length(), utf8_str.c_str());
				ret[len] = '\0';

				return ret;
			}
			else
			{
				return nullptr;
			}
		}

		virtual void FreeBuf(char *buf) override
		{
			if (buf)
			{
				rs_delete_array (buf);
			}
		}

		virtual bool DownloadFile(const char* download_url, const char* file_abs_path) override
		{
			System::String^ param_url = CNetTypeUtil::ToManagedString(download_url);
			rse::string mbcs_path;
			EncodeConvertor::UTF8ToMBCS(mbcs_path, file_abs_path);
			System::String^ param_file_path = CNetTypeUtil::ToManagedString(mbcs_path.c_str());
			return managed_ptr_->DownloadFile(param_url, param_file_path);
		}

		virtual void OnProgress(int category, int progress, const char* info) override
		{
			System::String^ dot_net_info = info ? CNetTypeUtil::ToManagedString(info) : nullptr;
			return managed_ptr_->OnProgress(category, progress, dot_net_info);
		}

		virtual bool GetReceiveProgress() override
		{
			return managed_ptr_->GetReceiveProgress();
		}

		virtual void SetReceiveProgress(bool val) override
		{
			managed_ptr_->SetReceiveProgress(val);
		}

		virtual void SetDownloadFileCount(int file_count) override
		{
			
		}
		gcroot<IDecorationVRCallBack^> managed_ptr_;
	};
}
