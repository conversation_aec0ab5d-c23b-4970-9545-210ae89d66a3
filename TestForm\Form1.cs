﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using TestDVNetWrapper;

namespace TestForm
{
    public partial class Form1 : Form
    {
        Test _test = new Test();
        public Form1()
        {
            InitializeComponent();
        }

        /*
         *   <elevatorsetting carwidth="192" cardepth="190" carheight="320" doorheight="240" doorwidth="110">
	<ConfigID id="1001"/>
	<ConfigID id="1004"/>
  </elevatorsetting>
  <elevatorsetting carwidth="170" cardepth="160" carheight="260" doorheight="230" doorwidth="105">
	<ConfigID id="1002"/>
	<ConfigID id="1003"/>
         * 
         */
        private void Form1_Load(object sender, EventArgs e)
        {
            _test.Initialize(panel1.Handle, panel1.Width, panel1.Height);
        }

        private void button1_Click(object sender, EventArgs e)
        {
            _test.ChangeConfig(1001);
        }

        private void button2_Click(object sender, EventArgs e)
        {
            _test.ChangeConfig(1002);
        }

        private void button3_Click(object sender, EventArgs e)
        {
            _test.ChangeConfig(1003);
        }

        private void button4_Click(object sender, EventArgs e)
        {
            _test.ChangeConfig(1004);
        }

        private void panel1_Resize(object sender, EventArgs e)
        {
            _test.Resize(panel1.Width, panel1.Height);
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            _test.Render();
        }


    }
}
