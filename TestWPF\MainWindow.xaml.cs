﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace TestWPF
{
    /// <summary>
    /// 测试前需要编译对应版本的 引擎，装潢程序（需要拷贝到plugin目录中），RseDiscoverGraph, DecorationVRInterface
    /// 然后将解决方案目录中“yd”文件夹拷贝到dll所在目录
    /// </summary>

    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        TestDVNetWrapper.Test _test = new TestDVNetWrapper.Test();
        Vr.DGHostWnd _vrWnd = new Vr.DGHostWnd();
        DispatcherTimer _timer = new DispatcherTimer();
        public MainWindow()
        {
            InitializeComponent();
        }

        private void con_Loaded(object sender, RoutedEventArgs e)
        {
            _vrWnd = new Vr.DGHostWnd(0, 0, (int)con.Width, (int)con.Height);
            _vrWnd.ResizeEvent += _vrWnd_ResizeEvent;
            con.Child = _vrWnd;
            _test.Initialize(_vrWnd.GetHandleRef(), (int)con.Width, (int)con.Height);

            _timer.Interval = TimeSpan.FromMilliseconds(30);
            _timer.Start();
            _timer.Tick += _timer_Tick;
        }

        void _timer_Tick(object sender, EventArgs e)
        {
            _test.Render();
        }

        private void _vrWnd_ResizeEvent(object sender, EventArgs e)
        {
            _test.Resize((int)con.Width, (int)con.Height);
        }
    }
}
