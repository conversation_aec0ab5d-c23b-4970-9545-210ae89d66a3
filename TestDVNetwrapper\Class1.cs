﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TestDVNetWrapper
{
    class Class1 : DVNetWrapper.IDecorationVRCallBack
    {
        public void LoadCompleteCallBack(int wparam, int vali, string vals)
        {
            System.IO.File.AppendAllText("d:\\a.txt", "LoadCompleteCallBack");
        }

        public string DownloadData(string download_url, string post_data)
        {
            throw new NotImplementedException();
        }

        public bool DownloadFile(string download_url, string file_abs_path)
        {
            throw new NotImplementedException();
        }
    }
}
