#include "stdafx.h"
#include "VrHostWnd.h"

namespace Vr
{
	DGHostWnd^ DGHostWnd::FindObject(HWND hwnd)
	{
		for each(DGHostWnd ^ obj in m_stArrObject)
		{
			if(obj->GetHwnd() == hwnd) return obj;
		}
		
		//::MessageBox(NULL, _T("Find HwndHost Object failed!!"), _T("CDGHostWnd::FindObject"), MB_OK);
		return nullptr;
	}

	LRESULT CALLBACK RenderWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
	{
		//static bool m_bFullScreen = false;

		switch (message) 
		{		
		case WM_SIZE:			// Resize the render context
			{
				DGHostWnd^ obj = DGHostWnd::FindObject(hWnd);
				if(obj)
				{
					RECT rc;
					::GetClientRect(hWnd, &rc);
					//::MoveWindow(obj->m_VrWnd->m_RenderWindow, rc.left, rc.top, rc.right - rc.left,
					//	rc.bottom - rc.top, TRUE);
					obj->TriggerResizeEvent();
				}
			}
			break;

		default:
			{
				if(message > WM_USER)
				{
					DGHostWnd^ obj = DGHostWnd::FindObject(hWnd);
					if(obj) obj->TriggerWndProcEvent(message, wParam, lParam);
				}
			}

			break;
		}

		return DefWindowProc(hWnd, message, wParam, lParam);
	}	

	//
	HandleRef DGHostWnd::BuildWindowCore(HandleRef hwndParent)
	{
		m_hWnd = CreateWindowEx(0,
			strSZ_MYWINDOW_CLASS, _T(""),
			(WS_CHILD | WS_OVERLAPPED | WS_VISIBLE) & ~WS_CAPTION,
			m_nX, m_nY, // x, y
			m_nWidth, m_nHeight, // height, width
			(HWND) hwndParent.Handle.ToPointer(), // parent hwnd
			NULL,
			NULL,
			0);

		assert(m_hWnd);

		return HandleRef(this, IntPtr(m_hWnd));
	}

	//
	ATOM DGHostWnd::MyRegisterClass()
	{
		WNDCLASS MyRenderClass;
		ZeroMemory(&MyRenderClass,sizeof(MyRenderClass));

		MyRenderClass.style = CS_HREDRAW | CS_VREDRAW | CS_DBLCLKS;
		MyRenderClass.lpfnWndProc = (WNDPROC)RenderWndProc;
		MyRenderClass.hInstance = GetModuleHandle(NULL);
		MyRenderClass.lpszClassName = strSZ_MYWINDOW_CLASS;

		return RegisterClass(&MyRenderClass);
	}

	void DGHostWnd::ClearArrObject()
	{
		m_stArrObject->Clear();
	}
}
