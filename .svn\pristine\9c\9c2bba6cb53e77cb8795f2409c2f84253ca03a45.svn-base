#pragma once

//#using <PresentationFramework.dll>
//#using <WindowsBase.dll>
//#using <PresentationCore.dll>
//#include "DGLibMgr.h"
using namespace System;
using namespace System::Collections::Generic;
using namespace System::Windows;
using namespace System::Windows::Controls;
using namespace System::Windows::Interop;
using namespace System::Runtime::InteropServices;

namespace Vr
{
	static const LPCTSTR strSZ_MYWINDOW_CLASS = _T("DGNetWrapperWnd");

	public ref class WndProcEventArgs : EventArgs
	{
	public:
		property int Message;
		property int WParam;
		property int LParam;
	};


	public ref class DGHostWnd : HwndHost
	{
	public:
		static DGHostWnd()
		{
			m_stArrObject = gcnew List<DGHostWnd^>();

			ATOM dwRet = MyRegisterClass();
			//assert(dwRet);

		}

		DGHostWnd()
		{
			m_stArrObject->Add(this);

			m_nX = m_nY = 0;
			m_nWidth = 70;
			m_nHeight = 30;
		}

		DGHostWnd(int x, int y, int width, int height)
		{
			m_stArrObject->Add(this);
			//MessageBox::Show("Test");
			//m_VrWnd = new CVrWnd();
			//m_VrWnd->_RegisterClass();

			SetCreateWindowRect(x, y, width, height);
		}

		event EventHandler<EventArgs^>^ ResizeEvent;
		event EventHandler<WndProcEventArgs^>^ WndprocEvent;
		
		IntPtr GetHandleRef() { return IntPtr(m_hWnd); }


		void SetCreateWindowRect(int x, int y, int width, int height) 
		{
			m_nX = x;
			m_nY = y;
			m_nWidth = width;
			m_nHeight = height;

		}

		void Destroy()
		{
//			m_VrWnd->Destroy();
// 			delete m_VrWnd;
// 			m_VrWnd = nullptr;

			DestroyWindow(m_hWnd);

			m_stArrObject->Clear();
		}

		static void ClearArrObject();
	private:
		int m_nX, m_nY, m_nWidth, m_nHeight;

	internal:
		HWND GetHwnd() { return m_hWnd; }
		static DGHostWnd^ FindObject(HWND hwnd);

		void TriggerResizeEvent()
		{
			ResizeEvent(this, nullptr);
		}

		void TriggerWndProcEvent(int m, int w, int l)
		{
			WndProcEventArgs^ p = gcnew WndProcEventArgs();
			p->Message = m;
			p->WParam = w;
			p->LParam = l;
			//if(resizeEvent)
			{
				WndprocEvent(this, p);
			}
		}

	protected:
		virtual HandleRef BuildWindowCore(HandleRef hwndParent) override;
		virtual void DestroyWindowCore(HandleRef hwnd) override 
		{
            // hwnd will be disposed for us
        }
	private:
		static ATOM MyRegisterClass();
	private:
		HWND m_hWnd;
	public:
		static List<DGHostWnd^>^ m_stArrObject;
	};
}
