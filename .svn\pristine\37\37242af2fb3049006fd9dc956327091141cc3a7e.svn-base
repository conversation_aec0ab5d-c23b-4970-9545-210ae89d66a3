#pragma once

#include "IntermediateConst.h"

using namespace System;

namespace DVNetWrapper
{
	public ref class ManagedMsgParaValue
	{
	public:
		property String^ Name;
		property ManagedMsgParaValueType ValueType;
		property Object^ Value;

		ManagedMsgParaValue(String^ name, ManagedMsgParaValueType t, float val)
		{
			Name = name;
			ValueType = t;
			Value = (Object^)val;
		}

		ManagedMsgParaValue(String^ name, ManagedMsgParaValueType t, UInt32 val)
		{
			Name = name;
			ValueType = t;
			Value = (Object^)val;
		}

		ManagedMsgParaValue(String^ name, ManagedMsgParaValueType t, Int32 val)
		{
			Name = name;
			ValueType = t;
			Value = (Object^)val;
		}

		ManagedMsgParaValue(String^ name, ManagedMsgParaValueType t, String^ val)
		{
			Name = name;
			ValueType = t;
			Value = (Object^)val;
		}

		ManagedMsgParaValue(String^ name, ManagedMsgParaValueType t, double val)
		{
			Name = name;
			ValueType = t;
			Value = (Object^)val;
		}
	};

	public ref class ManagedElevatorSpecification
	{
	public:
		property int ElevatorSizeId;
		property int Width;
		property int Depth;
		property int Height;
		property int DoorWidth;
		property int DoorHeight;
		property int ElevatorCount;

		ManagedElevatorSpecification()
		{
			ElevatorCount = 4;
		}

	};

	
}